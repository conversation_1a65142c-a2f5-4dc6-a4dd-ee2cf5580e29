package fm.lizhi.ocean.seal.api.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.api.GamePropService;
import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import fm.lizhi.ocean.seal.dao.bean.GamePropFlowBean;
import fm.lizhi.ocean.seal.manager.GamePropManager;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.*;
import fm.lizhi.ocean.seal.constant.GamePropGrantStatus;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 游戏道具服务实现类
 * <AUTHOR>
 */
@Slf4j
@ServiceProvider
public class GamePropServiceImpl implements GamePropService {

    @Inject
    private GamePropManager gamePropManager;

    @Override
    public Result<ResponseGrantGameProp> grantGameProp(GrantGamePropParam param) {
        log.info("Grant game prop, param: {}", param);

        try {
            // 参数校验
            if (param == null) {
                return new Result<>(GRANT_GAME_PROP_PARAM_ERROR, null);
            }

            if (param.getUserId() <= 0 || param.getPropId() <= 0 || param.getNum() <= 0) {
                log.warn("Invalid parameters: userId={}, propId={}, num={}",
                           param.getUserId(), param.getPropId(), param.getNum());
                return new Result<>(GRANT_GAME_PROP_PARAM_ERROR, null);
            }

            // 调用业务逻辑
            Long flowId = gamePropManager.grantGameProp(param);

            // 构建响应
            ResponseGrantGameProp response = ResponseGrantGameProp.newBuilder()
                .setGrantStatus(GamePropGrantStatus.SUCCESS.getCode())
                .setMessage("Grant success")
                .setFlowId(flowId)
                .build();

            return new Result<>(GRANT_GAME_PROP_SUCCESS, response);

        } catch (IllegalArgumentException e) {
            log.warn("Grant game prop parameter error: {}", e.getMessage());
            if (e.getMessage().contains("not exists")) {
                return new Result<>(GRANT_GAME_PROP_NOT_EXISTS, null);
            }
            return new Result<>(GRANT_GAME_PROP_PARAM_ERROR, null);
        } catch (RuntimeException e) {
            log.error("Grant game prop failed: {}", e.getMessage());
            ResponseGrantGameProp response = ResponseGrantGameProp.newBuilder()
                .setGrantStatus(GamePropGrantStatus.FAILED.getCode())
                .setMessage(e.getMessage())
                .build();
            return new Result<>(GRANT_GAME_PROP_FAIL, response);
        } catch (Exception e) {
            log.error("Grant game prop error", e);
            return new Result<>(GRANT_GAME_PROP_ERROR, null);
        }
    }

    @Override
    public Result<ResponseGetGamePropList> getGamePropList(GetGamePropListParam param) {
        log.info("Get game prop list, param: {}", param);

        try {
            // 参数校验
            if (param == null) {
                return new Result<>(GET_GAME_PROP_LIST_PARAM_ERROR, null);
            }

            // 查询道具列表
            List<GamePropBean> propList = gamePropManager.getGamePropList(param);
            int total = gamePropManager.countGameProp(param);

            // 转换为协议对象
            ResponseGetGamePropList.Builder responseBuilder = ResponseGetGamePropList.newBuilder();
            responseBuilder.setTotal(total);
            responseBuilder.setPageNumber(param.getPageNumber() > 0 ? param.getPageNumber() : 1);
            responseBuilder.setPageSize(param.getPageSize() > 0 ? param.getPageSize() : 20);

            for (GamePropBean propBean : propList) {
                GameProp gameProp = convertToGameProp(propBean);
                responseBuilder.addGameProps(gameProp);
            }

            return new Result<>(GET_GAME_PROP_LIST_SUCCESS, responseBuilder.build());

        } catch (Exception e) {
            log.error("Get game prop list error", e);
            return new Result<>(GET_GAME_PROP_LIST_ERROR, null);
        }
    }

    @Override
    public Result<ResponseGetGamePropFlowList> getGamePropFlowList(GetGamePropFlowListParam param) {
        log.info("Get game prop flow list, param: {}", param);

        try {
            // 参数校验
            if (param == null) {
                return new Result<>(GET_GAME_PROP_FLOW_LIST_PARAM_ERROR, null);
            }

            // 查询流水列表
            List<GamePropFlowBean> flowList = gamePropManager.getGamePropFlowList(param);
            int total = gamePropManager.countGamePropFlow(param);

            // 转换为协议对象
            ResponseGetGamePropFlowList.Builder responseBuilder = ResponseGetGamePropFlowList.newBuilder();
            responseBuilder.setTotal(total);
            responseBuilder.setPageNumber(param.getPageNumber() > 0 ? param.getPageNumber() : 1);
            responseBuilder.setPageSize(param.getPageSize() > 0 ? param.getPageSize() : 20);

            for (GamePropFlowBean flowBean : flowList) {
                GamePropFlow gamePropFlow = convertToGamePropFlow(flowBean);
                responseBuilder.addGamePropFlows(gamePropFlow);
            }

            return new Result<>(GET_GAME_PROP_FLOW_LIST_SUCCESS, responseBuilder.build());

        } catch (Exception e) {
            log.error("Get game prop flow list error", e);
            return new Result<>(GET_GAME_PROP_FLOW_LIST_ERROR, null);
        }
    }



    /**
     * 转换GamePropBean为GameProp协议对象
     */
    private GameProp convertToGameProp(GamePropBean bean) {
        GameProp.Builder builder = GameProp.newBuilder();
        if (bean.getId() != null) builder.setId(bean.getId());
        if (bean.getChannelGameId() != null) builder.setChannelGameId(bean.getChannelGameId());
        if (bean.getChannelPropId() != null) builder.setChannelPropId(bean.getChannelPropId());
        if (bean.getAppId() != null) builder.setAppId(bean.getAppId());
        if (bean.getChannelId() != null) builder.setChannelId(bean.getChannelId());
        if (bean.getName() != null) builder.setName(bean.getName());
        if (bean.getPropDesc() != null) builder.setPropDesc(bean.getPropDesc());
        if (bean.getType() != null) builder.setType(bean.getType());
        if (bean.getDurationSec() != null) builder.setDurationSec(bean.getDurationSec());
        if (bean.getTimeliness() != null) builder.setTimeliness(bean.getTimeliness());
        if (bean.getIconUrl() != null) builder.setIconUrl(bean.getIconUrl());
        if (bean.getRemark() != null) builder.setRemark(bean.getRemark());
        if (bean.getDeleted() != null) builder.setDeleted(bean.getDeleted());
        if (bean.getOperator() != null) builder.setOperator(bean.getOperator());
        if (bean.getCreateTime() != null) builder.setCreateTime(bean.getCreateTime().getTime());
        if (bean.getModifyTime() != null) builder.setModifyTime(bean.getModifyTime().getTime());
        return builder.build();
    }

    /**
     * 转换GamePropFlowBean为GamePropFlow协议对象
     */
    private GamePropFlow convertToGamePropFlow(GamePropFlowBean bean) {
        GamePropFlow.Builder builder = GamePropFlow.newBuilder();
        if (bean.getId() != null) builder.setId(bean.getId());
        if (bean.getPropId() != null) builder.setPropId(bean.getPropId());
        if (bean.getUserId() != null) builder.setUserId(bean.getUserId());
        if (bean.getUniqueId() != null) builder.setUniqueId(bean.getUniqueId());
        if (bean.getDurationSec() != null) builder.setDurationSec(bean.getDurationSec());
        if (bean.getChannelGameId() != null) builder.setChannelGameId(bean.getChannelGameId());
        if (bean.getChannelPropId() != null) builder.setChannelPropId(bean.getChannelPropId());
        if (bean.getNum() != null) builder.setNum(bean.getNum());
        if (bean.getAppId() != null) builder.setAppId(bean.getAppId());
        if (bean.getType() != null) builder.setType(bean.getType());
        if (bean.getGrantStatus() != null) builder.setGrantStatus(bean.getGrantStatus());
        if (bean.getCreateTime() != null) builder.setCreateTime(bean.getCreateTime().getTime());
        if (bean.getModifyTime() != null) builder.setModifyTime(bean.getModifyTime().getTime());
        return builder.build();
    }
}
