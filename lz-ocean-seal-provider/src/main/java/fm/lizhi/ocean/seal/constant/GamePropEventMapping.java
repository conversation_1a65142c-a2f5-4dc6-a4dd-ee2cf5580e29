package fm.lizhi.ocean.seal.constant;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 游戏道具事件映射
 * <AUTHOR>
 */
@Slf4j
@Getter
public enum GamePropEventMapping {

    // region sud
    /**
     * 道具发放 - SUD渠道
     */
    GRANT_PROP_SUD(GamePropEventType.GRANT_PROP.getEvent(), GameChannel.SUD, "grant_prop"),

    /**
     * 道具回收 - SUD渠道
     */
    REVOKE_PROP_SUD(GamePropEventType.REVOKE_PROP.getEvent(), GameChannel.SUD, "revoke_prop"),

    /**
     * 道具使用 - SUD渠道
     */
    USE_PROP_SUD(GamePropEventType.USE_PROP.getEvent(), GameChannel.SUD, "use_prop"),

    /**
     * 道具过期 - SUD渠道
     */
    EXPIRE_PROP_SUD(GamePropEventType.EXPIRE_PROP.getEvent(), GameChannel.SUD, "expire_prop"),

    // endregion

    // region luk
    /**
     * 道具发放 - LUK渠道
     */
    GRANT_PROP_LUK(GamePropEventType.GRANT_PROP.getEvent(), GameChannel.LUK, 10),

    /**
     * 道具回收 - LUK渠道
     */
    REVOKE_PROP_LUK(GamePropEventType.REVOKE_PROP.getEvent(), GameChannel.LUK, 11),

    /**
     * 道具使用 - LUK渠道
     */
    USE_PROP_LUK(GamePropEventType.USE_PROP.getEvent(), GameChannel.LUK, 12),

    /**
     * 道具过期 - LUK渠道
     */
    EXPIRE_PROP_LUK(GamePropEventType.EXPIRE_PROP.getEvent(), GameChannel.LUK, 13),

    // endregion
    ;

    /**
     * 平台事件定义
     */
    private final String sealEvent;

    /**
     * 渠道类型
     */
    private final String channel;

    /**
     * 渠道事件定义
     */
    private final Object channelEvent;

    GamePropEventMapping(String sealEvent, String channel, Object channelEvent) {
        this.sealEvent = sealEvent;
        this.channel = channel;
        this.channelEvent = channelEvent;
    }

    /**
     * 渠道事件转平台事件
     *
     * @param channel      渠道名称
     * @param channelEvent 渠道事件
     * @return 平台事件
     */
    public static GamePropEventType getSealEvent(String channel, Object channelEvent) {
        for (GamePropEventMapping eventMapping : values()) {
            if (eventMapping.channel.equals(channel) && eventMapping.channelEvent.equals(channelEvent)) {
                return GamePropEventType.from(eventMapping.sealEvent);
            }
        }
        return null;
    }

    /**
     * 平台事件转渠道事件
     *
     * @param channel   渠道名称
     * @param sealEvent 平台事件
     * @return 渠道事件
     */
    public static Object getChannelEvent(String channel, String sealEvent) {
        if (StrUtil.isBlank(channel) || StrUtil.isBlank(sealEvent)) {
            return null;
        }

        for (GamePropEventMapping eventMapping : values()) {
            if (eventMapping.channel.equals(channel) && eventMapping.sealEvent.equals(sealEvent)) {
                return eventMapping.channelEvent;
            }
        }
        return null;
    }

    /**
     * 查找指定渠道和事件的映射
     *
     * @param channel   渠道名称
     * @param sealEvent 平台事件
     * @return 事件映射
     */
    public static GamePropEventMapping findMapping(String channel, String sealEvent) {
        if (StrUtil.isBlank(channel) || StrUtil.isBlank(sealEvent)) {
            return null;
        }

        for (GamePropEventMapping mapping : values()) {
            if (mapping.channel.equals(channel) && mapping.sealEvent.equals(sealEvent)) {
                return mapping;
            }
        }
        return null;
    }
}
