package fm.lizhi.ocean.seal.gamechannel;

import fm.lizhi.ocean.seal.constant.PlatformMessageCodeEnum;
import fm.lizhi.ocean.seal.constant.TokenTypeEnum;
import fm.lizhi.ocean.seal.pojo.bo.ChannelCodeInfo;
import fm.lizhi.ocean.seal.pojo.bo.GameChannelAuthInfo;
import fm.lizhi.ocean.seal.pojo.bo.GameUidInfo;

/**
 * 渠道认证服务接口
 *
 * <AUTHOR>
 * @create: 2025/07/01 16:00
 */
public interface IGameChannelAuthorService {

    /**
     * 验证SSToken
     *
     * @param token
     * @return
     */
    PlatformMessageCodeEnum verifySSToke(String appId, String token);

    /**
     * 验证Code
     *
     * @param code
     * @return
     */
    PlatformMessageCodeEnum verifyCode(String appId, String code);

    /**
     * 获取渠道厂商Code
     *
     * @param appId
     * @param uid
     * @return
     */
    ChannelCodeInfo getCode(String appId, String uid);

    /**
     * 获取渠道厂商交互uid
     *
     * @param token
     * @param tokenType
     * @return
     */
    GameUidInfo getUid(String token, TokenTypeEnum tokenType);

    /**
     * 获取渠道厂商认证信息
     *
     * @param appId
     * @param token
     * @param timestamp
     * @param sign
     * @return
     */
    GameChannelAuthInfo getAuthInfo(String appId, String token, TokenTypeEnum tokenType, Integer timestamp, String sign);
}
