package fm.lizhi.ocean.seal.gamechannel;

import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.constant.PlatformMessageCodeEnum;
import fm.lizhi.ocean.seal.constant.TokenTypeEnum;
import fm.lizhi.ocean.seal.pojo.bo.ChannelCodeInfo;
import fm.lizhi.ocean.seal.pojo.bo.GameChannelAuthInfo;
import fm.lizhi.ocean.seal.pojo.bo.GameUidInfo;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: LUK渠道认证信息服务类
 * @author: guoyibin
 * @create: 2025/07/01 16:00
 */
@Slf4j
@AutoBindSingleton
public class ChannelLukAuthorService implements IGameChannelAuthorService{

    @Override
    public PlatformMessageCodeEnum verifySSToke(String appId, String token) {
        return null;
    }

    @Override
    public PlatformMessageCodeEnum verifyCode(String appId, String code) {
        return null;
    }

    @Override
    public ChannelCodeInfo getCode(String appId, String uid) {
        return null;
    }

    @Override
    public GameUidInfo getUid(String token, TokenTypeEnum tokenType) {
        return null;
    }

    @Override
    public GameChannelAuthInfo getAuthInfo(String appId, String token, TokenTypeEnum tokenType, Integer timestamp, String sign) {
        return null;
    }
}
