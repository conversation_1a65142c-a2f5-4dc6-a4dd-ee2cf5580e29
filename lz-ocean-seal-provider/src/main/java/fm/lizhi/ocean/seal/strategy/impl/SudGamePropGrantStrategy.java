package fm.lizhi.ocean.seal.strategy.impl;

import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.constant.GamePropEventMapping;
import fm.lizhi.ocean.seal.constant.GamePropEventType;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import fm.lizhi.ocean.seal.manager.HttpManager;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam;
import fm.lizhi.ocean.seal.strategy.GamePropGrantResult;
import fm.lizhi.ocean.seal.strategy.GamePropGrantStrategy;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * SUD 渠道道具发放策略实现
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class SudGamePropGrantStrategy implements GamePropGrantStrategy {

    @Inject
    private HttpManager httpManager;

    @Override
    public GamePropGrantResult grantProp(GrantGamePropParam param,
                                         GamePropBean gameProp,
                                         GameBizGameBean bizGameBean,
                                         GameInfoBean gameInfoBean,
                                         GameChannelBean gameChannelBean) {
        
        log.info("SUD grant prop, gameId: {}, propId: {}, userId: {}", 
                bizGameBean.getId(), gameProp.getId(), param.getUserId());

        try {
            // 构建请求参数
            String requestBody = buildRequestBody(param, gameProp, gameInfoBean, gameChannelBean);
            Map<String, String> headers = buildRequestHeaders(param, gameProp, gameInfoBean, gameChannelBean, requestBody);
            
            // 发送 HTTP 请求
            String response = httpManager.post(gameChannelBean.getCallbackUrl(), requestBody, headers);
            
            // 解析响应
            return parseResponse(response);
            
        } catch (Exception e) {
            log.error("SUD grant prop failed, gameId: {}, propId: {}, userId: {}", 
                     bizGameBean.getId(), gameProp.getId(), param.getUserId(), e);
            return GamePropGrantResult.failure(-1, "SUD grant prop failed: " + e.getMessage());
        }
    }

    @Override
    public boolean supports(String channel) {
        return GameChannel.SUD.equals(channel);
    }

    /**
     * 构建请求体
     */
    private String buildRequestBody(GrantGamePropParam param,
                                   GamePropBean gameProp,
                                   GameInfoBean gameInfoBean,
                                   GameChannelBean gameChannelBean) {
        
        String gameId = gameInfoBean.getChannelGameIdStr();
        String timestamp = System.currentTimeMillis() + "";
        
        // 获取 SUD 渠道事件
        Object channelEvent = GamePropEventMapping.getChannelEvent(GameChannel.SUD, GamePropEventType.GRANT_PROP.getEvent());
        if (channelEvent == null) {
            throw new IllegalArgumentException("No SUD event mapping found for grant prop");
        }
        
        // 构建数据部分
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("userId", param.getUserId());
        dataMap.put("propId", gameProp.getChannelPropId());
        dataMap.put("num", param.getNum());
        dataMap.put("uniqueId", param.getUniqueId());
        dataMap.put("durationSec", gameProp.getDurationSec());
        dataMap.put("remark", param.getRemark());
        
        // 构建请求体
        Map<String, Object> entry = new HashMap<>();
        entry.put("event", channelEvent);
        entry.put("mg_id", gameId);
        entry.put("timestamp", timestamp);
        entry.put("data", dataMap);
        
        return JsonUtil.dumps(entry);
    }

    /**
     * 构建请求头
     */
    private Map<String, String> buildRequestHeaders(GrantGamePropParam param,
                                                   GamePropBean gameProp,
                                                   GameInfoBean gameInfoBean,
                                                   GameChannelBean gameChannelBean,
                                                   String requestBody) {
        
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        
        String timestamp = System.currentTimeMillis() + "";
        String nonce = UUID.randomUUID().toString();
        
        // SUD 签名逻辑
        String signContent = String.format("%s\n%s\n%s\n%s\n", 
                                          gameChannelBean.getAppId(), timestamp, nonce, requestBody);
        HMac hMac = new HMac(HmacAlgorithm.HmacSHA1, gameChannelBean.getAppSecret().getBytes());
        String signature = hMac.digestHex(signContent);
        
        // 设置 SUD 认证头
        headers.put("Authorization", 
                   "Sud-Auth app_id=\""+gameChannelBean.getAppId()+
                   "\",nonce=\""+nonce+"\",timestamp=\""+timestamp+
                   "\",signature=\""+signature+"\"");
        
        return headers;
    }

    /**
     * 解析响应
     */
    private GamePropGrantResult parseResponse(String response) {
        try {
            JSONObject responseJson = JSONObject.parseObject(response);
            int retCode = responseJson.getIntValue("ret_code");
            String retMsg = responseJson.getString("ret_msg");
            
            if (retCode == 0) {
                return GamePropGrantResult.success(response);
            } else {
                return GamePropGrantResult.failure(retCode, retMsg);
            }
            
        } catch (Exception e) {
            log.error("Failed to parse SUD response: {}", response, e);
            return GamePropGrantResult.failure(-1, "Failed to parse response");
        }
    }
}
