package fm.lizhi.ocean.seal.manager;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameCallback;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import io.github.cfgametech.SDK;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

/**
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class LukManger {

    @Inject
    private GameChannelManager gameChannelManager;
    @Inject
    private GameCallbackManager gameCallbackManager;
    @Inject
    private BizGameManager bizGameManager;
    @Inject
    private GameInfoManager gameInfoManager;

    /**
     * 创建 LUK SDK 实例
     */
    public SDK createLukSdk(GameBizGameBean bizGameBean, GameChannelBean gameChannelBean, String appId) {
        try {
            GameCallback gameCallback = gameCallbackManager.getGameCallback(
                    appId,
                    String.valueOf(bizGameBean.getId()),
                    gameCallbackManager.getCallBackTypeByGameInterface(),
                    null);

            if (gameCallback == null) {
                log.error("Failed to get game callback for LUK SDK, gameId: {}, appId: {}",
                        bizGameBean.getId(), appId);
                return null;
            }

            return new SDK(gameChannelBean.getAppSecret(), gameCallback.getUrl());

        } catch (Exception e) {
            log.error("Error creating LUK SDK, gameId: {}, channelId: {}",
                    bizGameBean.getId(), gameChannelBean.getId(), e);
            return null;
        }
    }


    /**
     * 创建 LUK SDK 实例
     *
     * @param appId 应用ID
     * @return SDK 实例，如果无法创建则返回 null
     */
    public SDK createLukSdk(String appId, String gameId) {
        if (StringUtils.isBlank(appId) || StringUtils.isBlank(gameId)) {
            log.error("AppId is blank, cannot create LUK SDK");
            return null;
        }
        try {

            // 查询gameId是属于游戏渠道的gameId，还是属于seal平台biz_gameId
            GameBizGameBean bizGameBean = bizGameManager.getGame(appId, gameId);
            if (bizGameBean == null) {
                throw new IllegalArgumentException("gameId not exist. appId=" + appId + ", gameId=" + gameId);
            }

            // 获取游戏信息
            GameInfoBean gameInfoBean = gameInfoManager.getGameInfoBeanById(bizGameBean.getGameInfoId());
            if (gameInfoBean == null) {
                throw new IllegalArgumentException("gameInfo not exist. appId=" + appId + ", gameId=" + gameId);
            }

            // 获取渠道信息
            GameChannelBean gameChannelBean = gameChannelManager.getGameChannel(bizGameBean.getChannelId());
            if (gameChannelBean == null) {
                throw new IllegalArgumentException("channel not exist. appId=" + appId + ", gameId=" +gameId);
            }

            return createLukSdk(bizGameBean, gameChannelBean, appId);

        } catch (Exception e) {
            log.error("Error creating LUK SDK with appId: {}", appId, e);
            return null;
        }
    }

    /**
     * 获取 LUK 渠道的 SDK 实例
     *
     * @param channelId 渠道ID
     * @param gameId
     * @return SDK 实例
     */
    public Pair<SDK, GameChannelBean> createLukSdk(Long channelId, Long gameId, String appId){
        GameChannelBean gameChannel = gameChannelManager.getGameChannel(channelId);
        if(null == gameChannel){
            return null;
        }

        GameCallback gameCallback = gameCallbackManager.getGameCallback(appId, String.valueOf(gameId),
                gameCallbackManager.getCallBackTypeByGameInterface(), null
        );
        SDK sdk = new SDK(gameChannel.getAppSecret(), gameCallback.getUrl());
        return Pair.of(sdk, gameChannel);

    }



}
