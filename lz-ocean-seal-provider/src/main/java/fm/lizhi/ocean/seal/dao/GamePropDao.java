package fm.lizhi.ocean.seal.dao;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import fm.lizhi.ocean.seal.dao.bean.GamePropFlowBean;
import fm.lizhi.ocean.seal.dao.mapper.GamePropBeanMapper;
import fm.lizhi.ocean.seal.dao.mapper.GamePropFlowBeanMapper;
import fm.lizhi.ocean.seal.dao.mapper.ext.GamePropExtMapper;
import fm.lizhi.ocean.seal.dao.mapper.ext.GamePropFlowExtMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 游戏道具数据访问对象
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class GamePropDao {

    @Inject
    private GamePropBeanMapper gamePropBeanMapper;

    @Inject
    private GamePropExtMapper gamePropExtMapper;

    @Inject
    private GamePropFlowBeanMapper gamePropFlowBeanMapper;

    @Inject
    private GamePropFlowExtMapper gamePropFlowExtMapper;

    /**
     * 根据ID查询道具
     * @param id 道具ID
     * @return 道具信息
     */
    public GamePropBean selectByPrimaryKey(Long id) {
        log.debug("Select game prop by id: {}", id);
        return gamePropBeanMapper.selectByPrimaryKey(id);
    }

    /**
     * 根据渠道游戏ID和渠道道具ID查询道具
     * @param channelGameId 渠道游戏ID
     * @param channelPropId 渠道道具ID
     * @return 道具信息
     */
    public GamePropBean selectByChannelGameIdAndChannelPropId(Long channelGameId, Long channelPropId) {
        log.debug("Select game prop by channelGameId: {}, channelPropId: {}", channelGameId, channelPropId);
        return gamePropExtMapper.selectByChannelGameIdAndChannelPropId(channelGameId, channelPropId);
    }

    /**
     * 根据条件查询道具列表
     * @param channelGameId 渠道游戏ID
     * @param channelId 渠道ID
     * @param type 道具类型
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 道具列表
     */
    public List<GamePropBean> selectByConditions(Long channelGameId, Long channelId, Integer type, 
                                                Integer offset, Integer limit) {
        log.debug("Select game props by conditions: channelGameId={}, channelId={}, type={}, offset={}, limit={}", 
                 channelGameId, channelId, type, offset, limit);
        return gamePropExtMapper.selectByConditions(channelGameId, channelId, type, offset, limit);
    }

    /**
     * 根据条件统计道具数量
     * @param channelGameId 渠道游戏ID
     * @param channelId 渠道ID
     * @param type 道具类型
     * @return 道具数量
     */
    public int countByConditions(Long channelGameId, Long channelId, Integer type) {
        log.debug("Count game props by conditions: channelGameId={}, channelId={}, type={}", 
                 channelGameId, channelId, type);
        return gamePropExtMapper.countByConditions(channelGameId, channelId, type);
    }

    /**
     * 插入道具
     * @param record 道具信息
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int insert(GamePropBean record) {
        log.info("Insert game prop: {}", record.getName());
        record.setCreateTime(new Date());
        record.setModifyTime(new Date());
        return gamePropBeanMapper.insert(record);
    }

    /**
     * 选择性插入道具
     * @param record 道具信息
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(GamePropBean record) {
        log.info("Insert selective game prop: {}", record.getName());
        if (record.getCreateTime() == null) {
            record.setCreateTime(new Date());
        }
        if (record.getModifyTime() == null) {
            record.setModifyTime(new Date());
        }
        return gamePropBeanMapper.insertSelective(record);
    }

    /**
     * 根据ID更新道具
     * @param record 道具信息
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKey(GamePropBean record) {
        log.info("Update game prop by id: {}", record.getId());
        record.setModifyTime(new Date());
        return gamePropBeanMapper.updateByPrimaryKey(record);
    }

    /**
     * 根据ID选择性更新道具
     * @param record 道具信息
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKeySelective(GamePropBean record) {
        log.info("Update selective game prop by id: {}", record.getId());
        record.setModifyTime(new Date());
        return gamePropBeanMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 根据ID删除道具（逻辑删除）
     * @param id 道具ID
     * @param operator 操作人
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteByPrimaryKey(Long id, String operator) {
        log.info("Delete game prop by id: {}, operator: {}", id, operator);
        GamePropBean record = new GamePropBean();
        record.setId(id);
        record.setDeleted(true);
        record.setOperator(operator);
        record.setModifyTime(new Date());
        return gamePropBeanMapper.updateByPrimaryKeySelective(record);
    }

    // ==================== 道具流水相关方法 ====================

    /**
     * 根据ID查询流水
     * @param id 流水ID
     * @return 流水信息
     */
    public GamePropFlowBean selectFlowByPrimaryKey(Long id) {
        log.debug("Select game prop flow by id: {}", id);
        return gamePropFlowBeanMapper.selectByPrimaryKey(id);
    }

    /**
     * 根据唯一ID查询流水（用于幂等性检查）
     * @param uniqueId 唯一ID
     * @return 流水信息
     */
    public GamePropFlowBean selectFlowByUniqueId(String uniqueId) {
        log.debug("Select game prop flow by uniqueId: {}", uniqueId);
        return gamePropFlowExtMapper.selectByUniqueId(uniqueId);
    }

    /**
     * 根据条件查询流水列表
     * @param userId 用户ID
     * @param propId 道具ID
     * @param grantStatus 发放状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 流水列表
     */
    public List<GamePropFlowBean> selectFlowByConditions(Long userId, Long propId, Integer grantStatus,
                                                        Date startTime, Date endTime, Integer offset, Integer limit) {
        log.debug("Select game prop flows by conditions: userId={}, propId={}, grantStatus={}, startTime={}, endTime={}, offset={}, limit={}",
                 userId, propId, grantStatus, startTime, endTime, offset, limit);
        return gamePropFlowExtMapper.selectByConditions(userId, propId, grantStatus, startTime, endTime, offset, limit);
    }

    /**
     * 根据条件统计流水数量
     * @param userId 用户ID
     * @param propId 道具ID
     * @param grantStatus 发放状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 流水数量
     */
    public int countFlowByConditions(Long userId, Long propId, Integer grantStatus, Date startTime, Date endTime) {
        log.debug("Count game prop flows by conditions: userId={}, propId={}, grantStatus={}, startTime={}, endTime={}",
                 userId, propId, grantStatus, startTime, endTime);
        return gamePropFlowExtMapper.countByConditions(userId, propId, grantStatus, startTime, endTime);
    }

    /**
     * 插入流水
     * @param record 流水信息
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertFlow(GamePropFlowBean record) {
        log.info("Insert game prop flow: userId={}, propId={}, uniqueId={}",
                record.getUserId(), record.getPropId(), record.getUniqueId());
        record.setCreateTime(new Date());
        record.setModifyTime(new Date());
        return gamePropFlowBeanMapper.insert(record);
    }

    /**
     * 选择性插入流水
     * @param record 流水信息
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertFlowSelective(GamePropFlowBean record) {
        log.info("Insert selective game prop flow: userId={}, propId={}, uniqueId={}",
                record.getUserId(), record.getPropId(), record.getUniqueId());
        if (record.getCreateTime() == null) {
            record.setCreateTime(new Date());
        }
        if (record.getModifyTime() == null) {
            record.setModifyTime(new Date());
        }
        return gamePropFlowBeanMapper.insertSelective(record);
    }

    /**
     * 根据ID更新流水
     * @param record 流水信息
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateFlowByPrimaryKey(GamePropFlowBean record) {
        log.info("Update game prop flow by id: {}", record.getId());
        record.setModifyTime(new Date());
        return gamePropFlowBeanMapper.updateByPrimaryKey(record);
    }

    /**
     * 根据ID选择性更新流水
     * @param record 流水信息
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateFlowByPrimaryKeySelective(GamePropFlowBean record) {
        log.info("Update selective game prop flow by id: {}", record.getId());
        record.setModifyTime(new Date());
        return gamePropFlowBeanMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 更新流水状态
     * @param id 流水ID
     * @param grantStatus 发放状态
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateFlowGrantStatus(Long id, Integer grantStatus) {
        log.info("Update game prop flow grant status: id={}, grantStatus={}", id, grantStatus);
        return gamePropFlowExtMapper.updateGrantStatus(id, grantStatus);
    }

    /**
     * 根据用户ID和道具ID查询用户道具流水
     * @param userId 用户ID
     * @param propId 道具ID
     * @param grantStatus 发放状态（可选）
     * @return 流水列表
     */
    public List<GamePropFlowBean> selectFlowByUserIdAndPropId(Long userId, Long propId, Integer grantStatus) {
        log.debug("Select game prop flows by userId={}, propId={}, grantStatus={}", userId, propId, grantStatus);
        return gamePropFlowExtMapper.selectByUserIdAndPropId(userId, propId, grantStatus);
    }
}
