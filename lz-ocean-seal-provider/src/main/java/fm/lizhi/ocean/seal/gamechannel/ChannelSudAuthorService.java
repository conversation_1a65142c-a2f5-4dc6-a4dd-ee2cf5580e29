package fm.lizhi.ocean.seal.gamechannel;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.game.auth.SealAuth;
import fm.lizhi.ocean.seal.constant.ChannelType;
import fm.lizhi.ocean.seal.constant.PlatformMessageCodeEnum;
import fm.lizhi.ocean.seal.constant.SUDMessageCodeMappingEnum;
import fm.lizhi.ocean.seal.constant.TokenTypeEnum;
import fm.lizhi.ocean.seal.manager.GameAppManager;
import fm.lizhi.ocean.seal.pojo.bo.ChannelCodeInfo;
import fm.lizhi.ocean.seal.pojo.bo.GameChannelAuthInfo;
import fm.lizhi.ocean.seal.pojo.bo.GameUidInfo;
import lombok.extern.slf4j.Slf4j;
import tech.sud.mgp.auth.ErrorCodeEnum;
import tech.sud.mgp.auth.api.SudCode;
import tech.sud.mgp.auth.api.SudMGPAuth;
import tech.sud.mgp.auth.api.SudSSToken;
import tech.sud.mgp.auth.api.SudUid;

/**
 * @description: SUD渠道认证相关服务
 * @author: guoyibin
 * @create: 2025/07/01 16:57
 */
@Slf4j
@AutoBindSingleton
public class ChannelSudAuthorService implements IGameChannelAuthorService{

    @Inject
    private GameAppManager gameAppManager;

    @Override
    public PlatformMessageCodeEnum verifySSToke(String appId, String token) {
        SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(appId, ChannelType.SUD.getName());
        if (sealAuth == null) {
            log.warn("verifySSToke fail, sealAuth is null, appId: {}, token: {}", appId, token);
            return PlatformMessageCodeEnum.NOT_FOUND;
        }

        SudMGPAuth sudMGPAuth = new SudMGPAuth(sealAuth.getAppId(), sealAuth.getAppSecret());
        ErrorCodeEnum errorCodeEnum = sudMGPAuth.verifySSToken(token);
        return SUDMessageCodeMappingEnum.findBySudCode(errorCodeEnum.getCode());
    }

    @Override
    public PlatformMessageCodeEnum verifyCode(String appId, String code) {
        SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(appId, ChannelType.SUD.getName());
        if (sealAuth == null) {
            log.warn("verifyCode fail, sealAuth is null, appId: {}, code: {}", appId, code);
            return PlatformMessageCodeEnum.NOT_FOUND;
        }

        SudMGPAuth sudMGPAuth = new SudMGPAuth(sealAuth.getAppId(), sealAuth.getAppSecret());
        ErrorCodeEnum errorCodeEnum = sudMGPAuth.verifyCode(code);
        return SUDMessageCodeMappingEnum.findBySudCode(errorCodeEnum.getCode());
    }

    @Override
    public ChannelCodeInfo getCode(String appId, String uid) {
        SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(appId, ChannelType.SUD.getName());
        if (sealAuth == null) {
            log.warn("getCode fail, sealAuth is null, appId: {}, uid: {}", appId, uid);
            return null;
        }
        SudMGPAuth sudMGPAuth = new SudMGPAuth(sealAuth.getAppId(), sealAuth.getAppSecret());
        SudCode sudCode = sudMGPAuth.getCode(uid);
        return new ChannelCodeInfo().setChannel(ChannelType.SUD.getName()).setCode(sudCode.getCode()).setExpireTime(sudCode.getExpireDate());
    }

    @Override
    public GameUidInfo getUid(String token, TokenTypeEnum tokenType) {
        SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(token, ChannelType.SUD.getName());
        if (sealAuth == null) {
            log.warn("getUid fail, sealAuth is null, token: {}, tokenType: {}", token, tokenType);
            // TODO:待确认这个code列表，同时注意上游是否有使用这个code做什么操作
            return new GameUidInfo().setSuccess(false).setErrorCode(ErrorCodeEnum.UNDEFINE.getCode());
        }
        SudMGPAuth sudMGPAuth = new SudMGPAuth(sealAuth.getAppId(), sealAuth.getAppSecret());
        SudUid sudUid = getUidByToken(sudMGPAuth, token, tokenType);
        if (sudUid.isSuccess()) {
            return new GameUidInfo().setSuccess(true).setErrorCode(sudUid.getErrorCode()).setUid(sudUid.getUid());
        }
        
        return new GameUidInfo().setSuccess(false).setErrorCode(ErrorCodeEnum.UNDEFINE.getCode());
    }

    @Override
    public GameChannelAuthInfo getAuthInfo(String appId, String token, TokenTypeEnum tokenType, Integer timestamp, String sign) {
        SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(appId, ChannelType.SUD.getName());
        if (sealAuth == null) {
            log.warn("getAuthInfo fail, sealAuth is null, appId: {}, token: {}, tokenType: {}", appId, token, tokenType);
            return new GameChannelAuthInfo().setSuccess(false).setErrorCode(PlatformMessageCodeEnum.NOT_FOUND.getCode());
        }
        SudMGPAuth sudMGPAuth = new SudMGPAuth(sealAuth.getAppId(), sealAuth.getAppSecret());

        PlatformMessageCodeEnum platformMessageCodeEnum = verifyToken(sudMGPAuth, token, tokenType);
        if (platformMessageCodeEnum != PlatformMessageCodeEnum.SUCCESS) {
            return new GameChannelAuthInfo().setSuccess(false).setErrorCode(platformMessageCodeEnum.getCode());
        }

        SudUid sudUid = getUidByToken(sudMGPAuth, token, tokenType);
        if (sudUid == null || !sudUid.isSuccess()) {
            //TODO:确认这里的错误码是否被上游使用，需要映射一份平台的错误码（uid相关）
           return new GameChannelAuthInfo().setSuccess(false).setErrorCode(ErrorCodeEnum.UNDEFINE.getCode());
        }

        SudSSToken sudSSToken = sudMGPAuth.getSSToken(sudUid.getUid());
        return new GameChannelAuthInfo().setSuccess(true).setErrorCode(0)
                .setChannel(ChannelType.SUD.getName())
                .setSsToken(sudSSToken.getToken())
                .setExpireTime(sudSSToken.getExpireDate())
                .setUid(sudUid.getUid());

    }

    private PlatformMessageCodeEnum verifyToken(SudMGPAuth sudMGPAuth, String token, TokenTypeEnum tokenType) {
        if (TokenTypeEnum.CHANNEL_SS_TOKEN.equals(tokenType)) {
            return SUDMessageCodeMappingEnum.findBySudCode(sudMGPAuth.verifySSToken(token).getCode());
        } else if (TokenTypeEnum.CHANNEL_CODE.equals(tokenType)) {
            return SUDMessageCodeMappingEnum.findBySudCode(sudMGPAuth.verifyCode(token).getCode());
        }
        return PlatformMessageCodeEnum.UNDEFINE;
    }


    private SudUid getUidByToken(SudMGPAuth sudMGPAuth, String token, TokenTypeEnum tokenType) {
        if (TokenTypeEnum.CHANNEL_SS_TOKEN.equals(tokenType)) {
            return sudMGPAuth.getUidBySSToken(token);
        } else if (TokenTypeEnum.CHANNEL_CODE.equals(tokenType)) {
            return sudMGPAuth.getUidByCode(token);
        }
        return null;
    }

}
