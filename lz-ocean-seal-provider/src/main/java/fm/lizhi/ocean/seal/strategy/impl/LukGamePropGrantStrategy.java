package fm.lizhi.ocean.seal.strategy.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.constant.GamePropEventMapping;
import fm.lizhi.ocean.seal.constant.GamePropEventType;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import fm.lizhi.ocean.seal.manager.LukManger;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam;
import fm.lizhi.ocean.seal.strategy.GamePropGrantResult;
import fm.lizhi.ocean.seal.strategy.GamePropGrantStrategy;
import io.github.cfgametech.Response;
import io.github.cfgametech.SDK;
import io.github.cfgametech.beans.PublishControlEventRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * LUK 渠道道具发放策略实现
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class LukGamePropGrantStrategy implements GamePropGrantStrategy {

    @Inject
    private LukManger lukManager;

    @Override
    public GamePropGrantResult grantProp(GrantGamePropParam param,
                                         GamePropBean gameProp,
                                         GameBizGameBean bizGameBean,
                                         GameInfoBean gameInfoBean,
                                         GameChannelBean gameChannelBean) {
        
        log.info("LUK grant prop, gameId: {}, propId: {}, userId: {}", 
                bizGameBean.getId(), gameProp.getId(), param.getUserId());

        try {
            // 创建 LUK SDK 实例
            SDK lukSdk = lukManager.createLukSdk(bizGameBean, gameChannelBean, param.getAppId());
            if (lukSdk == null) {
                throw new RuntimeException("Failed to create LUK SDK");
            }
            
            // 调用 LUK SDK 发放道具
            Response<?> sdkResult = invokeLukSdk(lukSdk, param, gameProp, gameInfoBean, gameChannelBean);
            
            // 转换结果
            return convertLukResult(sdkResult);
            
        } catch (Exception e) {
            log.error("LUK grant prop failed, gameId: {}, propId: {}, userId: {}", 
                     bizGameBean.getId(), gameProp.getId(), param.getUserId(), e);
            return GamePropGrantResult.failure(-1, "LUK grant prop failed: " + e.getMessage());
        }
    }

    @Override
    public boolean supports(String channel) {
        return GameChannel.LUK.equals(channel);
    }

    /**
     * 调用 LUK SDK
     */
    private Response<?> invokeLukSdk(SDK lukSdk,
                                   GrantGamePropParam param,
                                   GamePropBean gameProp,
                                   GameInfoBean gameInfoBean,
                                   GameChannelBean gameChannelBean) throws Exception {
        
        // 获取 LUK 渠道事件
        Object channelEvent = GamePropEventMapping.getChannelEvent(GameChannel.LUK, GamePropEventType.GRANT_PROP.getEvent());
        if (channelEvent == null) {
            throw new IllegalArgumentException("No LUK event mapping found for grant prop");
        }
        
        // 构建数据
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("userId", param.getUserId());
        dataMap.put("propId", gameProp.getChannelPropId());
        dataMap.put("num", param.getNum());
        dataMap.put("uniqueId", param.getUniqueId());
        dataMap.put("durationSec", gameProp.getDurationSec());
        dataMap.put("remark", param.getRemark());
        
        // 构建 LUK SDK 请求
        PublishControlEventRequest.Builder builder = new PublishControlEventRequest.Builder();
        builder.setAppId(Integer.parseInt(gameChannelBean.getAppId()));
        builder.setGameId(Math.toIntExact(gameInfoBean.getChannelGameId()));
        builder.setRoomId(MapUtil.getStr(dataMap, "room_id", ""));
        builder.setTimestamp(System.currentTimeMillis());
        builder.setType((Integer) channelEvent);
        builder.setData(JSONObject.toJSONString(dataMap));
        
        return lukSdk.PublishControlEvent(builder.build());
    }

    /**
     * 转换 LUK 结果
     */
    private GamePropGrantResult convertLukResult(Response<?> sdkResult) {
        try {
            if (sdkResult != null) {
                if (sdkResult.suc()) {
                    return GamePropGrantResult.success(JsonUtil.dumps(sdkResult.getData()));
                } else {
                    return GamePropGrantResult.failure(sdkResult.getCode(), sdkResult.getMessage());
                }
            } else {
                return GamePropGrantResult.failure(-1, "LUK SDK returned null result");
            }
            
        } catch (Exception e) {
            log.error("Error converting LUK result", e);
            return GamePropGrantResult.failure(-1, "Failed to convert LUK response: " + e.getMessage());
        }
    }
}
