package fm.lizhi.ocean.seal.api;

import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGrantGameProp;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropList;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.RequestGetGamePropFlowList;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropFlowList;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam;

/**
 * 游戏道具相关接口
 * <AUTHOR>
 */
public interface GamePropService {

    /**
     * 道具发放接口
     *
     * @param param 道具发放参数
     * @return
     *     //if rcode == 0 发放成功<br>
     *     //if rcode == 1 参数错误<br>
     *     //if rcode == 2 道具不存在<br>
     *     //if rcode == 3 用户不存在<br>
     *     //if rcode == 4 发放失败<br>
     *     //if rcode == 5 内部错误<br>
     */
    @Service(domain = 4302, op = 250, request = RequestGrantGameProp.class, response = ResponseGrantGameProp.class)
    @Return(resultType = ResponseGrantGameProp.class)
    Result<ResponseGrantGameProp> grantGameProp(@Attribute(name = "param") GrantGamePropParam param);

    /**
     * 获取道具列表
     *
     * @param param 查询参数
     * @return
     *     //if rcode == 0 查询成功<br>
     *     //if rcode == 1 参数错误<br>
     *     //if rcode == 2 内部错误<br>
     */
    @Service(domain = 4302, op = 251, request = RequestGetGamePropList.class, response = ResponseGetGamePropList.class)
    @Return(resultType = ResponseGetGamePropList.class)
    Result<ResponseGetGamePropList> getGamePropList(@Attribute(name = "param") GetGamePropListParam param);

    /**
     * 获取道具流水列表
     *
     * @param param 查询参数
     * @return
     *     //if rcode == 0 查询成功<br>
     *     //if rcode == 1 参数错误<br>
     *     //if rcode == 2 内部错误<br>
     */
    @Service(domain = 4302, op = 252, request = RequestGetGamePropFlowList.class, response = ResponseGetGamePropFlowList.class)
    @Return(resultType = ResponseGetGamePropFlowList.class)
    Result<ResponseGetGamePropFlowList> getGamePropFlowList(@Attribute(name = "param") GetGamePropFlowListParam param);



    // 返回码常量
    public static final int GRANT_GAME_PROP_SUCCESS = 0; // 发放成功
    public static final int GRANT_GAME_PROP_PARAM_ERROR = 1; // 参数错误
    public static final int GRANT_GAME_PROP_NOT_EXISTS = 2; // 道具不存在
    public static final int GRANT_GAME_PROP_USER_NOT_EXISTS = 3; // 用户不存在
    public static final int GRANT_GAME_PROP_FAIL = 4; // 发放失败
    public static final int GRANT_GAME_PROP_ERROR = 5; // 内部错误

    public static final int GET_GAME_PROP_LIST_SUCCESS = 0; // 查询成功
    public static final int GET_GAME_PROP_LIST_PARAM_ERROR = 1; // 参数错误
    public static final int GET_GAME_PROP_LIST_ERROR = 2; // 内部错误

    public static final int GET_GAME_PROP_FLOW_LIST_SUCCESS = 0; // 查询成功
    public static final int GET_GAME_PROP_FLOW_LIST_PARAM_ERROR = 1; // 参数错误
    public static final int GET_GAME_PROP_FLOW_LIST_ERROR = 2; // 内部错误
}
