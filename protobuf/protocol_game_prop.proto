package fm.lizhi.commons.template.datacenter.protocol;

option java_package = "fm.lizhi.ocean.seal.protocol";
option java_outer_classname = "GamePropServiceProto";

// 道具信息
message GameProp {
    optional int64 id = 1;
    optional int64 channelGameId = 2;
    optional int64 channelPropId = 3;
    optional int64 appId = 4;
    optional int64 channelId = 5;
    optional string name = 6;
    optional string propDesc = 7;
    optional int32 type = 8; // 道具类型, 1皮肤, 2道具
    optional int32 durationSec = 9; // 有效时长（单位：秒），小于 0 为永久
    optional bool timeliness = 10; // 是否是时效性道具
    optional string iconUrl = 11;
    optional string remark = 12;
    optional bool deleted = 13;
    optional string operator = 14;
    optional int64 createTime = 15;
    optional int64 modifyTime = 16;
}

// 道具流水信息
message GamePropFlow {
    optional int64 id = 1;
    optional int64 propId = 2;
    optional int64 userId = 3;
    optional string uniqueId = 4; // 幂等的唯一 ID
    optional int32 durationSec = 5; // 有效时长（单位：秒），小于 0 代表永久
    optional int64 channelGameId = 6;
    optional int64 channelPropId = 7;
    optional int32 num = 8; // 发放数量
    optional int64 appId = 9;
    optional int32 type = 10; // 道具类型, 1皮肤 2道具
    optional int32 grantStatus = 11; // 发放状态，0未发放，1发放中，2成功，3失败
    optional int64 createTime = 12;
    optional int64 modifyTime = 13;
}

// 道具发放参数
message GrantGamePropParam {
    optional int64 userId = 1; // 用户ID
    optional int64 propId = 2; // 道具ID
    optional int32 num = 3; // 发放数量
    optional string uniqueId = 4; // 幂等的唯一 ID
    optional string appId = 5; // 应用ID
    optional string gameId = 6; // 游戏ID
    optional string channel = 7; // 渠道
    optional string remark = 8; // 备注
}

// 道具列表查询参数
message GetGamePropListParam {
    optional int64 channelGameId = 1; // 渠道游戏ID
    optional int64 channelId = 2; // 渠道ID
    optional int32 type = 3; // 道具类型
    optional int32 pageNumber = 4; // 页码
    optional int32 pageSize = 5; // 页大小
}

// 道具流水查询参数
message GetGamePropFlowListParam {
    optional int64 userId = 1; // 用户ID
    optional int64 propId = 2; // 道具ID
    optional int32 grantStatus = 3; // 发放状态
    optional int64 startTime = 4; // 开始时间
    optional int64 endTime = 5; // 结束时间
    optional int32 pageNumber = 6; // 页码
    optional int32 pageSize = 7; // 页大小
}



// 请求消息
message RequestGrantGameProp {
    optional GrantGamePropParam param = 1;
}

message RequestGetGamePropList {
    optional GetGamePropListParam param = 1;
}

message RequestGetGamePropFlowList {
    optional GetGamePropFlowListParam param = 1;
}



// 响应消息
message ResponseGrantGameProp {
    optional int32 grantStatus = 1; // 发放状态，0未发放，1发放中，2成功，3失败
    optional string message = 2; // 消息
    optional int64 flowId = 3; // 流水ID
}

message ResponseGetGamePropList {
    repeated GameProp gameProps = 1;
    optional int32 total = 2;
    optional int32 pageNumber = 3;
    optional int32 pageSize = 4;
}

message ResponseGetGamePropFlowList {
    repeated GamePropFlow gamePropFlows = 1;
    optional int32 total = 2;
    optional int32 pageNumber = 3;
    optional int32 pageSize = 4;
}


